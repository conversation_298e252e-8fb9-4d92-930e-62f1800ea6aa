# 核心任务

1.  **规则内化**: 深度理解并记忆 [融合平台迁移](./融合平台迁移)文档中的所有规则。
2.  **代码扫描**: 逐行分析代码文件。
3.  **精准匹配**: 将代码中的每一行、每一个代码块与内化的规则进行严格匹配，给出具体的匹配结果。
4.  **任务生成与输出**: 一旦发现匹配项，**立即**按照下方指定的 **“输出格式”** 生成一条任务记录。将所有生成的记录连续输出。
5.  新建一个文件，将所有生成的任务写入 `批次能耗插件开发任务清单.md`

# 输出格式 (Output Format)

对于每一个识别出的任务，你**必须**严格按照以下格式生成，不得有任何偏差：

**序号**: [任务序号]
**文件名称**: `[文件路径]`
**行号**: [具体行号或范围]
**问题类型**: [问题分类]
**描述**: [详细的任务描述]

---

**示例 (Example):**
**序号**: 16
**文件名称**: `addOrEdit.vue`
**行号**: 415-417
**问题类型**: SCSS 样式修改
**描述**: 将 `@include background_color(BG);` `@include padding(J1);` 修改为对应的 CSS 变量

---
